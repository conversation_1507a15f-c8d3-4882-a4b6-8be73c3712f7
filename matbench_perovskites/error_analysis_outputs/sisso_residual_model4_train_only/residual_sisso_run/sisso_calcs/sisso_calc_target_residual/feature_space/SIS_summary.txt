# FEAT_ID     Score                   Feature Expression
0             0.340696769774311015    ((feature_47 - feature_45) / (|feature_41 - feature_15|))
1             0.340779274313787295    ((feature_36^6) / (|feature_41 - feature_15|))
2             0.340780753368866707    ((feature_36^6) / (feature_41 - feature_15))
3             0.347276956687653848    ((|feature_45 - feature_24|) / (feature_41 - feature_15))
4             0.347305349557378518    ((feature_45 - feature_24) / (feature_41 - feature_15))
5             0.350428355757968713    ((feature_45 - feature_24) / (|feature_41 - feature_15|))
6             0.350543776343605229    ((|feature_45 - feature_24|) / (|feature_41 - feature_15|))
7             0.353031537039110721    ((feature_10 / feature_6) / (|feature_41 - feature_15|))
8             0.353183872220249662    ((feature_11 / feature_6) / (|feature_41 - feature_15|))
9             0.354648095862769186    ((feature_10 / feature_6) / (feature_41 - feature_15))
10            0.35483981460822045     ((feature_11 / feature_6) / (feature_41 - feature_15))
11            0.367560550922799267    ((feature_8 / feature_6) / (|feature_41 - feature_15|))
12            0.367679509952728467    ((feature_9 / feature_6) / (|feature_41 - feature_15|))
13            0.368842947596500881    ((feature_8 / feature_6) / (feature_41 - feature_15))
14            0.368967686463832822    ((feature_9 / feature_6) / (feature_41 - feature_15))
#-----------------------------------------------------------------------
15            0.0766667648806443752   ((feature_49 * feature_29) / (|feature_43 - feature_39|))
16            0.0779042964667698151   ((feature_49 * feature_10) / (feature_43 - feature_39))
17            0.0779063826007038718   ((feature_49 * feature_11) / (feature_43 - feature_39))
18            0.0779233415943447333   ((feature_49 * feature_11) / (feature_43 + feature_39))
19            0.0779255124430829754   ((feature_49 * feature_10) / (feature_43 + feature_39))
20            0.0779398561160840886   ((feature_49 / feature_43) * (feature_40 * feature_11))
21            0.0780851629621317628   ((feature_49 * feature_11) / (|feature_43 - feature_39|))
22            0.0780874260224172639   ((feature_49 * feature_10) / (|feature_43 - feature_39|))
23            0.0783002824564328209   ((feature_49 * feature_26) / (feature_43 - feature_39))
24            0.0783016149223694502   ((feature_49 * feature_27) / (feature_43 - feature_39))
25            0.0783175882470433099   ((feature_49 * feature_27) / (feature_43 + feature_39))
26            0.078319039888731623    ((feature_49 * feature_26) / (feature_43 + feature_39))
27            0.078327502243069877    ((feature_49 * feature_26) * (feature_40 / feature_43))
28            0.0783741872229262587   ((feature_49 * feature_27) / (|feature_43 - feature_39|))
29            0.0783756509550916497   ((feature_49 * feature_26) / (|feature_43 - feature_39|))
#-----------------------------------------------------------------------
30            0.0721369480573524208   (|(|feature_47 - feature_40|) - (|feature_45 - feature_13|)|)
31            0.0721523251059588006   (|(feature_47 - feature_45) - (feature_15 / feature_13)|)
32            0.0728411509850528383   (|(feature_47 - feature_45) - (feature_20 / feature_12)|)
33            0.0729294501916373933   (|(feature_47 - feature_45) - sqrt(feature_23)|)
34            0.0731150296577363357   (|(|feature_47 - feature_22|) - (|feature_45 - feature_36|)|)
35            0.0732628639472331505   (|(feature_47 - feature_45) - sqrt(feature_15)|)
36            0.0733953448969879574   (exp(feature_47) - (feature_21 * feature_20))
37            0.0733959585452001739   (|(feature_47 - feature_45) - sqrt(feature_16)|)
38            0.0735038612355831122   (|(feature_47 - feature_45) - sqrt(feature_14)|)
39            0.0736141307336249523   (|(|feature_48 - feature_47|) - (|feature_45 - feature_22|)|)
40            0.0743394398889265645   (|(feature_47 - feature_45) - (feature_42 / feature_30)|)
41            0.0789009921078441118   ((feature_21 - feature_7) / (|feature_45 - feature_38|))
42            0.0789091130429723786   ((feature_30 - feature_21) / (|feature_45 - feature_38|))
43            0.078913291252826881    ((|feature_21 - feature_7|) / (|feature_45 - feature_38|))
44            0.0803754666832013631   ((feature_47 - feature_45) * (feature_21 - feature_7))
#-----------------------------------------------------------------------
45            0.0399832738399829718   ((feature_16 + feature_1) / (feature_46 - feature_31))
46            0.0400607055229737427   ((feature_45 - feature_21) / (feature_22^6))
47            0.0400882305054205343   ((feature_39 + feature_1) / (feature_46 - feature_31))
48            0.0402267778901465867   ((feature_15 + feature_1) / (feature_46 - feature_31))
49            0.0403566782512146185   (|(feature_47 + feature_1) - (feature_37 / feature_6)|)
50            0.0405862782027839805   ((feature_47 / feature_4) / (feature_37 - feature_15))
51            0.0416598162586167692   ((feature_45 * feature_9) / (feature_41 - feature_15))
52            0.0418937343418084446   ((feature_45 * feature_8) / (feature_41 - feature_15))
53            0.0420332026271801121   ((feature_47 - feature_21) / (feature_22^6))
54            0.0436383540151578306   ((feature_45 * feature_9) / (|feature_41 - feature_15|))
55            0.0438671024649377322   ((feature_45 * feature_8) / (|feature_41 - feature_15|))
56            0.044108029973145238    ((feature_45 - feature_4) / (|feature_46 - feature_31|))
57            0.0446186909276397037   ((feature_47 - feature_4) / (|feature_46 - feature_31|))
58            0.0451960276767766761   ((feature_47 - feature_24) / (feature_46 - feature_31))
59            0.045876652436178611    ((feature_45 - feature_24) / (feature_46 - feature_31))
#-----------------------------------------------------------------------
60            0.0404101444646423821   ((feature_15 * feature_1) / (feature_46 - feature_31))
61            0.0404744956033762301   ((feature_3 * feature_1) / (feature_46 - feature_31))
62            0.0404786845056715333   ((feature_47 * feature_1) / (feature_46 - feature_31))
63            0.0405691617513118732   ((feature_45 * feature_1) / (feature_46 - feature_31))
64            0.0405692189523324809   ((|feature_37 - feature_1|) / (feature_46 - feature_31))
65            0.040741414574522225    ((feature_23 + feature_1) / (feature_46 - feature_31))
66            0.0410135051015039082   ((feature_1 / feature_34) / (feature_46 - feature_31))
67            0.0410665676651092229   ((feature_40 - feature_1) / (feature_46 - feature_31))
68            0.0414382903754695747   ((|feature_42 - feature_24|) / (feature_46 - feature_31))
69            0.041484421534795847    ((exp(-1.0 * feature_25)) / (feature_46 - feature_31))
70            0.0415217187701929294   ((feature_42 - feature_24) / (feature_46 - feature_31))
71            0.0416643477281587876   ((feature_1 / feature_33) / (feature_46 - feature_31))
72            0.0416660467896917894   ((feature_14 + feature_1) / (feature_46 - feature_31))
73            0.041805512432712133    ((feature_3 / feature_41) / (feature_46 - feature_31))
74            0.0422169907127292743   ((exp(-1.0 * feature_24)) / (feature_46 - feature_31))
#-----------------------------------------------------------------------
