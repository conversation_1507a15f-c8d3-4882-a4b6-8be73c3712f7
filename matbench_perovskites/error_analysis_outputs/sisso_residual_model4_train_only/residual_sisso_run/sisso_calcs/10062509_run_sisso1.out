Job started on Mon Aug 11 01:49:41 CEST 2025
Running on node(s): cnm025
Starting batch execution of sisso++
Entering folder: sisso_calc_target_residual
Commit: 686a99a5c703c438e64a2fe5b256efb0579c1986
Compiler: GNU version 13.3.0
Compiler flags:  -fopenmp
time input_parsing: 1.89097 s
Time to generate feat space: 235.628 s
Projection time: 77.1582 s
Time to get best features on rank : 0.56349 s
|---Complete final combination/selection from all ranks: 0.172045 s
Time for SIS: 79.4195 s
Time for l0-norm: 0.00582799 s
Projection time: 81.9956 s
Time to get best features on rank : 0.0525572 s
|---Complete final combination/selection from all ranks: 0.131502 s
Time for SIS: 83.6552 s
Time for l0-norm: 0.00536501 s
Projection time: 83.2337 s
Time to get best features on rank : 0.367754 s
|---Complete final combination/selection from all ranks: 0.172832 s
Time for SIS: 85.3233 s
Time for l0-norm: 0.0711667 s
Projection time: 82.7514 s
Time to get best features on rank : 0.342339 s
|---Complete final combination/selection from all ranks: 0.246973 s
Time for SIS: 84.8791 s
Time for l0-norm: 2.59803 s
Projection time: 82.6208 s
Time to get best features on rank : 0.270769 s
|---Complete final combination/selection from all ranks: 0.187303 s
Time for SIS: 84.6449 s
Time for l0-norm: 102.186 s
Train RMSE: 0.0219835 Unitless; Test RMSE: 0.0182977 Unitless
c0 + a0 * ((feature_9 / feature_6) / (feature_41 - feature_15))

Train RMSE: 0.0211044 Unitless; Test RMSE: 0.0183031 Unitless
c0 + a0 * ((feature_49 * feature_26) / (|feature_43 - feature_39|)) + a1 * ((feature_9 / feature_6) / (feature_41 - feature_15))

Train RMSE: 0.0202313 Unitless; Test RMSE: 0.0180058 Unitless
c0 + a0 * ((|feature_21 - feature_7|) / (|feature_45 - feature_38|)) + a1 * ((feature_49 * feature_26) / (|feature_43 - feature_39|)) + a2 * ((feature_9 / feature_6) / (feature_41 - feature_15))

Train RMSE: 0.0195559 Unitless; Test RMSE: 0.02876 Unitless
c0 + a0 * ((|feature_21 - feature_7|) / (|feature_45 - feature_38|)) + a1 * ((feature_49 * feature_26) / (|feature_43 - feature_39|)) + a2 * ((feature_36^6) / (feature_41 - feature_15)) + a3 * ((feature_47 - feature_45) / (|feature_41 - feature_15|))

Train RMSE: 0.0190993 Unitless; Test RMSE: 0.0285215 Unitless
c0 + a0 * ((feature_47 - feature_24) / (feature_46 - feature_31)) + a1 * ((|feature_21 - feature_7|) / (|feature_45 - feature_38|)) + a2 * ((feature_49 * feature_10) / (|feature_43 - feature_39|)) + a3 * ((feature_36^6) / (feature_41 - feature_15)) + a4 * ((feature_47 - feature_45) / (|feature_41 - feature_15|))

All tasks completed.

Resources Used

Total Memory used                        - MEM              : 14GiB
Total CPU Time                           - CPU_Time         : 05:05:12
Execution Time                           - Wall_Time        : 00:12:43
total programme cpu time                 - Total_CPU        : 05:02:40
Total_CPU / CPU_Time  (%)                - ETA              : 99%
Number of alloc CPU                      - NCPUS            : 24
Number of Mobilized Resources per Job    - NCPUS_EQUIV_PJOB : 27
Mobilized Resources x Execution Time     - R_Wall_Time      : 05:43:21
CPU_Time / R_Wall_Time (%)               - ALPHA            : 88%
Energy (Joules)                                             : unknown

