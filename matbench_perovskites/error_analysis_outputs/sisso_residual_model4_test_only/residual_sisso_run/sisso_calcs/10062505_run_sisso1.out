Job started on Mon Aug 11 01:49:11 CEST 2025
Running on node(s): cnm024
Starting batch execution of sisso++
Entering folder: sisso_calc_target_residual
Commit: 686a99a5c703c438e64a2fe5b256efb0579c1986
Compiler: GNU version 13.3.0
Compiler flags:  -fopenmp
time input_parsing: 0.0802726 s
Time to generate feat space: 103.53 s
Projection time: 28.9256 s
Time to get best features on rank : 0.000568816 s
|---Complete final combination/selection from all ranks: 0.0664485 s
Time for SIS: 31.9235 s
Time for l0-norm: 0.00576362 s
Projection time: 31.4335 s
Time to get best features on rank : 0.000389169 s
|---Complete final combination/selection from all ranks: 0.0628362 s
Time for SIS: 34.0082 s
Time for l0-norm: 0.00385897 s
Projection time: 31.7666 s
Time to get best features on rank : 0.000556813 s
|---Complete final combination/selection from all ranks: 0.0861339 s
Time for SIS: 34.998 s
Time for l0-norm: 0.0222192 s
Projection time: 31.6945 s
Time to get best features on rank : 0.00045314 s
|---Complete final combination/selection from all ranks: 0.0735057 s
Time for SIS: 35.0304 s
Time for l0-norm: 0.743556 s
Projection time: 31.6492 s
Time to get best features on rank : 0.000447227 s
|---Complete final combination/selection from all ranks: 0.060388 s
Time for SIS: 34.9853 s
Time for l0-norm: 29.7041 s
Train RMSE: 0.093361 Unitless; Test RMSE: 0.0565897 Unitless
c0 + a0 * ((feature_21 / feature_22) / (feature_35 - feature_21))

Train RMSE: 0.0864517 Unitless; Test RMSE: 0.226003 Unitless
c0 + a0 * ((feature_7 * feature_6) / (|feature_48 - feature_19|)) + a1 * ((feature_21 / feature_22) / (feature_35 - feature_21))

Train RMSE: 0.0812604 Unitless; Test RMSE: 0.230791 Unitless
c0 + a0 * ((feature_29 / feature_9) / (feature_44 + feature_37)) + a1 * ((feature_7 * feature_6) / (|feature_48 - feature_19|)) + a2 * ((feature_21 / feature_22) / (feature_35 - feature_21))

Train RMSE: 0.0764007 Unitless; Test RMSE: 0.229898 Unitless
c0 + a0 * ((feature_48 - feature_23) / (feature_35 - feature_34)) + a1 * ((feature_29 / feature_9) / (feature_44 + feature_37)) + a2 * ((feature_7 * feature_6) / (|feature_48 - feature_19|)) + a3 * ((feature_21 / feature_22) / (feature_35 - feature_21))

Train RMSE: 0.0724278 Unitless; Test RMSE: 0.2623 Unitless
c0 + a0 * ((feature_34 / feature_9) / (feature_48 - feature_3)) + a1 * ((feature_48 + feature_14) / (feature_35 - feature_34)) + a2 * ((feature_20 / feature_9) / (feature_44 + feature_32)) + a3 * ((feature_7 * feature_6) / (|feature_48 - feature_19|)) + a4 * ((feature_21 / feature_22) / (feature_35 - feature_21))

All tasks completed.

Resources Used

Total Memory used                        - MEM              : 14GiB
Total CPU Time                           - CPU_Time         : 02:04:24
Execution Time                           - Wall_Time        : 00:05:11
total programme cpu time                 - Total_CPU        : 02:01:54
Total_CPU / CPU_Time  (%)                - ETA              : 97%
Number of alloc CPU                      - NCPUS            : 24
Number of Mobilized Resources per Job    - NCPUS_EQUIV_PJOB : 27
Mobilized Resources x Execution Time     - R_Wall_Time      : 02:19:57
CPU_Time / R_Wall_Time (%)               - ALPHA            : 88%
Energy (Joules)                                             : unknown

