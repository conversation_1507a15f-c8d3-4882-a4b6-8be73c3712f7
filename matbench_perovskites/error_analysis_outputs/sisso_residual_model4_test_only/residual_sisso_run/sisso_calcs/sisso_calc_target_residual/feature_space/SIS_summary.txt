# FEAT_ID     Score                   Feature Expression
0             0.122735507509237146    ((feature_18^2) / (feature_50 - feature_45))
1             0.122857519391582043    ((|feature_29 - feature_16|) / (feature_50 - feature_45))
2             0.122980034102734836    ((feature_29 - feature_16) / (feature_50 - feature_45))
3             0.123288540396071775    ((feature_44 * feature_23) / (feature_50 - feature_45))
4             0.123348365626881062    ((feature_18 * feature_6) / (feature_50 - feature_45))
5             0.123805892560441444    ((feature_44 * feature_20) / (feature_50 - feature_45))
6             0.123807196002289774    ((feature_44 / feature_37) / (feature_15 - feature_10))
7             0.123899464936561426    ((feature_34 - feature_18) / (feature_50 - feature_45))
8             0.124314358985857257    ((feature_21 / feature_22) / (feature_50 - feature_45))
9             0.12869291505044872     ((feature_44 * feature_35) / (feature_35 - feature_21))
10            0.129932767928796433    ((feature_44 / feature_32) / (feature_50 - feature_45))
11            0.130269889628769925    ((feature_44 / feature_35) / (feature_50 - feature_45))
12            0.149473867720617493    ((feature_44 * feature_21) / (feature_35 - feature_21))
13            0.158178458817548079    ((feature_8 / feature_22) / (feature_35 - feature_21))
14            0.175039020946547691    ((feature_21 / feature_22) / (feature_35 - feature_21))
#-----------------------------------------------------------------------
15            0.134193162415824935    ((feature_18 * feature_3) / (|feature_48 - feature_19|))
16            0.134328339668830288    ((feature_18 * feature_5) / (|feature_48 - feature_19|))
17            0.134425979949126251    ((feature_6 * feature_3) / (feature_48 - feature_19))
18            0.134604767712562856    ((feature_18 * feature_2) / (|feature_48 - feature_19|))
19            0.134623907348242672    ((feature_6 * feature_4) / (feature_48 - feature_19))
20            0.134843523932501791    ((feature_6 * feature_2) / (feature_48 - feature_19))
21            0.135144258884438928    ((feature_18 * feature_7) / (|feature_48 - feature_19|))
22            0.135429547687093199    ((feature_33 - feature_1) / (|feature_48 - feature_19|))
23            0.135880102664705654    ((|feature_33 - feature_1|) / (|feature_48 - feature_19|))
24            0.136528551285109184    ((feature_6 * feature_3) / (|feature_48 - feature_19|))
25            0.136593865594514891    ((|feature_17 - feature_1|) / (|feature_48 - feature_19|))
26            0.136722043434511525    ((feature_6 * feature_4) / (|feature_48 - feature_19|))
27            0.136963199112783623    ((feature_6 * feature_2) / (|feature_48 - feature_19|))
28            0.139534644296110011    ((feature_7 * feature_6) / (feature_48 - feature_19))
29            0.14121741191294343     ((feature_7 * feature_6) / (|feature_48 - feature_19|))
#-----------------------------------------------------------------------
30            0.10970248140729752     ((feature_7 / feature_9) / (feature_44 + feature_37))
31            0.109799841430657866    ((feature_34 / feature_9) / (feature_44 + feature_37))
32            0.1101499137281335      ((feature_45 / feature_6) / (feature_44 + feature_37))
33            0.110166770772007316    ((exp(-1.0 * feature_26)) / (feature_37 * feature_18))
34            0.110568697243793063    ((feature_20 / feature_37) / (feature_32 + feature_9))
35            0.110777564724171163    ((exp(-1.0 * feature_26)) / (feature_35 * feature_18))
36            0.112371651157460492    ((feature_20 / feature_9) / (feature_44 + feature_32))
37            0.112762245221623078    ((feature_22 / feature_9) / (feature_44 + feature_37))
38            0.112965546796385846    ((feature_11 / feature_9) / (feature_44 + feature_37))
39            0.113563068327069777    ((feature_47 / feature_9) / (feature_44 + feature_37))
40            0.113782352629487077    ((feature_22 - feature_8) / (feature_32 * feature_6))
41            0.113854388978069848    ((feature_17 / feature_9) / (feature_44 + feature_37))
42            0.113939943311953992    ((feature_43 / feature_9) / (feature_44 + feature_37))
43            0.115712587714554241    ((feature_16 / feature_9) / (feature_44 + feature_37))
44            0.116459372637418943    ((feature_29 / feature_9) / (feature_44 + feature_37))
#-----------------------------------------------------------------------
45            0.100017416979864715    ((feature_23 - feature_3) / (feature_35 - feature_34))
46            0.100147605660804037    ((feature_48 - feature_23) / (|feature_35 - feature_34|))
47            0.100151953231976468    ((feature_40 - feature_23) / (|feature_35 - feature_34|))
48            0.103173448489311773    ((feature_33 - feature_17) / (|feature_35 - feature_34|))
49            0.104850673631973548    ((feature_38 - feature_29) / (feature_35 - feature_34))
50            0.105669677802519263    ((feature_38 - feature_29) / (|feature_35 - feature_34|))
51            0.108361863000296044    ((feature_33 - feature_17) / (feature_35 - feature_34))
52            0.110268709591549038    ((feature_20 + feature_14) / (feature_35 - feature_34))
53            0.110720779484907828    (ln(feature_12) / (feature_35 - feature_34))
54            0.111126339547370126    ((feature_40 - feature_20) / (feature_35 - feature_34))
55            0.112203227627626911    ((feature_23 + feature_14) / (feature_35 - feature_34))
56            0.112613858325521454    ((feature_40 - feature_23) / (feature_35 - feature_34))
57            0.113652366421404763    ((feature_48 + feature_14) / (feature_35 - feature_34))
58            0.113682400592269237    ((feature_48 - feature_20) / (feature_35 - feature_34))
59            0.115968847581408144    ((feature_48 - feature_23) / (feature_35 - feature_34))
#-----------------------------------------------------------------------
60            0.0830731816734244649   ((feature_37 / feature_32) / (|feature_38 - feature_29|))
61            0.0832141001789562379   ((feature_29 / feature_9) / (feature_48 - feature_3))
62            0.083608646723038596    ((feature_10 / feature_9) / (feature_48 - feature_3))
63            0.0841024333736392904   ((feature_44 / feature_9) / (feature_48 - feature_3))
64            0.0842505247753268655   ((feature_34 * feature_3) / (|feature_44 - feature_18|))
65            0.0843729541672551681   ((feature_34 / feature_44) / (feature_44 - feature_18))
66            0.0847410830154222      ((feature_34 * feature_2) / (feature_44 - feature_18))
67            0.0847814392141432588   ((feature_18 / feature_6) / (feature_48 - feature_3))
68            0.0858969709389539177   ((feature_12 / feature_9) / (feature_48 - feature_3))
69            0.0887621369231733626   ((feature_34 / feature_44) / (|feature_44 - feature_18|))
70            0.0888429007455469322   ((feature_34 * feature_7) / (|feature_44 - feature_18|))
71            0.0896660449175573637   ((feature_16 / feature_9) / (feature_48 - feature_3))
72            0.0907928390527459467   ((feature_33 / feature_9) / (feature_48 - feature_3))
73            0.0951523638157714263   ((feature_34 * feature_2) / (|feature_44 - feature_18|))
74            0.100037415140804706    ((feature_34 / feature_9) / (feature_48 - feature_3))
#-----------------------------------------------------------------------
