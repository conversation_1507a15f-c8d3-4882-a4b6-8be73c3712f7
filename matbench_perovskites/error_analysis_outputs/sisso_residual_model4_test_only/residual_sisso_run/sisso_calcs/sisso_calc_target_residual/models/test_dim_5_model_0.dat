# c0 + a0 * ((feature_34 / feature_9) / (feature_48 - feature_3)) + a1 * ((feature_48 + feature_14) / (feature_35 - feature_34)) + a2 * ((feature_20 / feature_9) / (feature_44 + feature_32)) + a3 * ((feature_7 * feature_6) / (|feature_48 - feature_19|)) + a4 * ((feature_21 / feature_22) / (feature_35 - feature_21))
# Property Label: $target$; Unit of the Property: Unitless
# RMSE: 0.262299611501028; Max AE: 1.39424118461701
# Coefficients
# Task   a0                      a1                      a2                      a3                      a4                      c0
# all , -3.053155961393495e-04, -1.360492908051302e-04, -7.276418992428529e-09, -3.371892435926600e+00,  5.988880205990827e-05,  2.127120825697952e-03, 
# Feature Rung, Units, and Expressions
# 0;  2; Unitless;                                         33|8|div|47|2|sub|div; ((feature_34 / feature_9) / (feature_48 - feature_3)); $\left(\frac{ \left(\frac{ feature_{34} }{ feature_{9} } \right) }{ \left(feature_{48} - feature_{3}\right) } \right)$; ((feature_34 ./ feature_9) ./ (feature_48 - feature_3)); ((newdata$feature_34 / newdata$feature_9) / (newdata$feature_48 - newdata$feature_3)); feature_34,feature_9,feature_48,feature_3
# 1;  2; Unitless;                                         47|13|add|34|33|sub|div; ((feature_48 + feature_14) / (feature_35 - feature_34)); $\left(\frac{ \left(feature_{48} + feature_{14}\right) }{ \left(feature_{35} - feature_{34}\right) } \right)$; ((feature_48 + feature_14) ./ (feature_35 - feature_34)); ((newdata$feature_48 + newdata$feature_14) / (newdata$feature_35 - newdata$feature_34)); feature_48,feature_14,feature_35,feature_34
# 2;  2; Unitless;                                         19|8|div|43|31|add|div; ((feature_20 / feature_9) / (feature_44 + feature_32)); $\left(\frac{ \left(\frac{ feature_{20} }{ feature_{9} } \right) }{ \left(feature_{44} + feature_{32}\right) } \right)$; ((feature_20 ./ feature_9) ./ (feature_44 + feature_32)); ((newdata$feature_20 / newdata$feature_9) / (newdata$feature_44 + newdata$feature_32)); feature_20,feature_9,feature_44,feature_32
# 3;  2; Unitless;                                         6|5|mult|47|18|abd|div; ((feature_7 * feature_6) / (|feature_48 - feature_19|)); $\left(\frac{ \left(feature_{7} feature_{6}\right) }{ \left(\left|feature_{48} - feature_{19}\right|\right) } \right)$; ((feature_7 .* feature_6) ./ abs(feature_48 - feature_19)); ((newdata$feature_7 * newdata$feature_6) / abs(newdata$feature_48 - newdata$feature_19)); feature_7,feature_6,feature_48,feature_19
# 4;  2; Unitless;                                         20|21|div|34|20|sub|div; ((feature_21 / feature_22) / (feature_35 - feature_21)); $\left(\frac{ \left(\frac{ feature_{21} }{ feature_{22} } \right) }{ \left(feature_{35} - feature_{21}\right) } \right)$; ((feature_21 ./ feature_22) ./ (feature_35 - feature_21)); ((newdata$feature_21 / newdata$feature_22) / (newdata$feature_35 - newdata$feature_21)); feature_21,feature_22,feature_35,feature_21
# Number of Samples Per Task
# Task, n_samples               
# all , 39                    
# Excluded Indexes: [ 5, 22, 29, 40, 43, 59, 89, 93, 94, 106, 113, 120, 139, 146, 151, 156, 157, 170, 174, 187, 192, 204, 236, 251, 266, 276, 279, 295, 300, 315, 327, 329, 338, 344, 346, 365, 366, 371, 385 ]

# Sample ID , Property Value        ,  Property Value (EST) ,  Feature 0 Value      ,  Feature 1 Value      ,  Feature 2 Value      ,  Feature 3 Value      ,  Feature 4 Value      
5           ,  9.466554641723630e-02, -1.346138630287526e-02,  9.941924969039223e-02,  2.804410521719789e+00,  1.785314731274391e+04,  0.000000000000000e+00, -2.512440944009393e+02
22          , -3.187671422958370e-02, -4.531144796261290e-03, -6.670547321531687e-01, -2.331896018353377e+00,  2.877839638124523e+03,  2.358499428053995e-03,  1.326401742191175e+01
29          , -1.075058937072754e-01,  1.125413542434771e-04,  1.437491657591006e+00,  9.523610929830232e+00,  3.982174953689853e+03,  9.312902170246802e-05,  1.051727184000851e+00
40          ,  8.543024063110349e-02, -3.457687501295385e-02, -5.146291301669260e+00,  6.156830021819037e+01,  3.903246329352452e+06,  2.036990823324127e-04, -1.353188089830065e+01
43          ,  3.863149225711820e-02, -2.094769779345954e-02,  2.034539314505807e-01,  1.487209861691809e+02,  3.598458807324229e+04,  8.715791815300722e-05, -3.712864714453157e+01
59          ,  3.299631357192980e-02, -3.471186448005424e-01,  4.204112973264247e+01,  7.229566409426349e+00,  4.566031810877196e+07,  1.341573678921137e-05, -5.238931553813319e+01
89          , -1.727662086486816e-01, -1.567007393265694e+00, -7.823106860961834e-02,  3.093928113481194e+00,  1.338519054713417e+02,  4.656115973481563e-01,  2.096760272715245e+01
93          , -8.234624862670700e-03,  8.364077483837890e-04, -5.201338203586292e-01, -7.057845667807142e-01,  2.372714230216093e+03,  5.759756256669337e-04,  6.910360027202898e+00
94          ,  3.739838123321520e-02, -7.811600399283311e-03, -1.130898764810499e+00,  8.931432666577570e-01,  1.367109991849044e+03,  3.545867033126846e-03,  3.011813700315033e+01
106         , -2.166919708251800e-03,  3.314662118926787e-03, -9.355355240318478e-01, -1.650513553943143e+00,  1.370721920244359e+03,  1.028739780934251e-03,  6.939745325422615e+01
113         ,  4.808726310729970e-02,  5.971514733076351e-03, -3.795518186884646e+00, -2.413758187721068e+01,  4.342696344602277e+04,  1.398781752013695e-04,  3.161009226268678e+00
120         ,  1.428375244140400e-03,  1.997895299860544e-03, -1.627325421291278e-01, -2.123264221177190e+00,  7.005804166403425e+03,  1.844877235834075e-04,  3.427536011300174e+00
139         , -3.616887092590330e-02,  1.686675679896414e-04, -6.960557864679227e-01,  3.661883629040474e-01,  5.854425976156385e+02,  7.919881640528209e-04,  9.243940908515077e+00
146         ,  1.264972686767590e-02,  2.572112438259939e-03, -2.358601450217029e-01, -1.630851001986112e+00,  9.081008378887533e+03,  1.556482703259819e-04,  1.238979794626133e+01
151         , -4.500385761260990e-02,  5.729115775586296e-03, -8.254513266854449e+00,  1.400341804158308e+00,  5.127693203754925e+03,  2.427760550778363e-04,  3.553593885792424e+01
156         ,  5.496220588684100e-03,  7.453654396088629e-04, -3.693151173789868e-04,  7.772467988453082e-01,  1.045483007772908e+04,  3.785588523385170e-04,  1.275850981727999e+00
157         , -7.247723817825311e-02,  4.313881436091197e-04, -2.185055063591958e-01, -1.246441997593430e+00,  2.281574159388525e+03,  8.379365392661613e-04,  1.519499749467101e+01
170         ,  5.851058483123770e-02,  1.510748987027695e-03,  2.900259775992689e-02,  2.678613956980651e+00,  1.862491639489790e+04,  4.591062968061031e-05,  7.887123244175265e-01
174         ,  5.844178199768000e-03, -2.898963331026673e-01, -3.326193053293390e+01, -4.836522339433429e+01,  4.224576126826471e+07,  1.284147450015067e-07, -2.271880102282117e+01
187         ,  2.110298633575430e-02,  2.759293252126244e-03, -9.652373158239242e-01, -4.913578797578761e+00,  4.240068692907209e+04,  5.872910349604037e-05,  2.931013589819728e+00
192         ,  2.425221443176270e-02, -1.352662343898134e-01, -2.753987507578483e+01, -2.824157571467901e+00,  2.015260326585105e+07,  2.295205532727654e-06,  7.690771801242554e+00
204         ,  1.126888275146480e-02,  1.953515671513455e-03, -6.201220857628589e-01, -2.902962462398094e-01,  1.138913955939867e+03,  3.206263814169269e-04,  1.147079528837664e+01
236         , -1.151049613952620e-02, -3.332354789419499e-02, -2.527380183802320e+02, -8.245316211493638e-01,  1.563897444130902e+07,  6.028805945940818e-06,  1.817292521376949e+01
251         , -2.776774406433110e-02, -5.806414852384694e-04, -4.693721984714686e-02, -2.300956091913441e+00,  1.628933917651078e+03,  1.096584623437077e-03,  1.125889580273221e+01
266         , -4.275083541869000e-04,  6.608998685686881e-03, -1.004335835719718e+00, -1.075919571455836e+01,  8.303183874863874e+04,  4.118525405366041e-05,  5.768197217552079e+01
276         ,  3.945925712585450e-02,  2.546101703126465e-03, -2.793058453237947e+00, -1.369759339283491e+00,  3.961762543169002e+03,  2.451896685809193e-04,  3.931326208117106e+00
279         ,  4.145424842834460e-02,  2.875363681125137e-03, -3.417246138219540e-01, -4.400421337322112e+00,  1.831903102072371e+04,  8.384538515447940e-05,  7.701763074616744e+00
295         , -2.707870721817010e-02, -9.003350615146479e-03,  1.989408998053684e-01,  4.907213615715179e+00,  5.231110572984905e+04,  2.945509205946612e-04, -1.507506728177557e+02
300         ,  2.831182956695550e-02,  6.590922149593295e-04,  8.231069578460824e+00, -9.737545206177510e+00,  6.053723810278832e+04,  6.058439452684020e-05,  6.095255592951237e+00
315         ,  4.429840087890620e-02, -1.808352180603273e-02,  2.746445493232856e-02,  1.025848239998670e+02,  5.379470170965802e+03,  1.911470469660317e-04, -9.287202282740424e+01
327         ,  1.536174297332770e-02, -5.096550695414472e-03,  2.259478394631598e+01,  2.239869555681941e+00,  1.442927363898994e+04,  3.543546020748794e-05,  3.407633273915394e+00
329         , -3.447393894195540e-02,  6.168470767787450e-04, -1.066814563894641e-01, -4.086489501331130e+00,  5.306961793663162e+03,  7.978148497790121e-04,  1.051869887603752e+01
338         , -1.185667872428895e-01,  1.026603833636235e-01, -3.396388521556905e+02,  1.393500297261201e+00,  4.508577618628737e+05,  1.029812997305437e-06,  5.174774522197035e+00
344         ,  3.363956689834590e-02, -2.632408508015980e-03, -2.725628071324582e-02, -2.813279974244456e+00,  5.654740449149252e+03,  1.630730482921337e-03,  6.498680574188772e+00
346         , -9.675530433654780e-02, -6.919467414682074e-01,  2.066366655213183e+01,  1.041689559298397e+01,  9.413729879387164e+07,  1.825541951606975e-07, -2.278668005711860e+01
365         ,  4.853692054748600e-03,  9.086296381371769e-04, -4.338098747939589e-01, -5.120270711307131e+00,  3.918120014257794e+03,  6.842083670007080e-04,  4.809540067062341e+00
366         ,  7.350245952606201e-02,  1.913635900919122e-03, -5.609444542033889e-01, -3.886227579633390e+00,  2.639907402694013e+04,  2.569141012358106e-04,  2.419632918949496e+00
371         ,  2.015067577362060e-02,  2.478064597743596e-03, -2.284299125939549e+00, -6.902652269999151e-01,  2.256221643729665e+04,  2.709930311767195e-04,  1.064527922297602e+01
385         ,  4.244400501251220e-02, -5.701315985578923e-03, -2.366369532847751e+00,  5.992880169022735e+01,  4.775046191438882e+04,  1.466332642711708e-04,  7.417556875663383e+00
