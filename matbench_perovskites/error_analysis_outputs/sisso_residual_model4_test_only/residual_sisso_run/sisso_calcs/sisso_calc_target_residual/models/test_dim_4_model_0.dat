# c0 + a0 * ((feature_48 - feature_23) / (feature_35 - feature_34)) + a1 * ((feature_29 / feature_9) / (feature_44 + feature_37)) + a2 * ((feature_7 * feature_6) / (|feature_48 - feature_19|)) + a3 * ((feature_21 / feature_22) / (feature_35 - feature_21))
# Property Label: $target$; Unit of the Property: Unitless
# RMSE: 0.229898070847294; Max AE: 1.39007455601025
# Coefficients
# Task   a0                      a1                      a2                      a3                      c0
# all , -2.546571506532790e-05, -1.996439107603061e-08, -3.364699939870576e+00,  5.968190918788863e-05,  2.783846880465593e-03, 
# Feature Rung, Units, and Expressions
# 0;  2; Unitless;                                         47|22|sub|34|33|sub|div; ((feature_48 - feature_23) / (feature_35 - feature_34)); $\left(\frac{ \left(feature_{48} - feature_{23}\right) }{ \left(feature_{35} - feature_{34}\right) } \right)$; ((feature_48 - feature_23) ./ (feature_35 - feature_34)); ((newdata$feature_48 - newdata$feature_23) / (newdata$feature_35 - newdata$feature_34)); feature_48,feature_23,feature_35,feature_34
# 1;  2; Unitless;                                         28|8|div|43|36|add|div; ((feature_29 / feature_9) / (feature_44 + feature_37)); $\left(\frac{ \left(\frac{ feature_{29} }{ feature_{9} } \right) }{ \left(feature_{44} + feature_{37}\right) } \right)$; ((feature_29 ./ feature_9) ./ (feature_44 + feature_37)); ((newdata$feature_29 / newdata$feature_9) / (newdata$feature_44 + newdata$feature_37)); feature_29,feature_9,feature_44,feature_37
# 2;  2; Unitless;                                         6|5|mult|47|18|abd|div; ((feature_7 * feature_6) / (|feature_48 - feature_19|)); $\left(\frac{ \left(feature_{7} feature_{6}\right) }{ \left(\left|feature_{48} - feature_{19}\right|\right) } \right)$; ((feature_7 .* feature_6) ./ abs(feature_48 - feature_19)); ((newdata$feature_7 * newdata$feature_6) / abs(newdata$feature_48 - newdata$feature_19)); feature_7,feature_6,feature_48,feature_19
# 3;  2; Unitless;                                         20|21|div|34|20|sub|div; ((feature_21 / feature_22) / (feature_35 - feature_21)); $\left(\frac{ \left(\frac{ feature_{21} }{ feature_{22} } \right) }{ \left(feature_{35} - feature_{21}\right) } \right)$; ((feature_21 ./ feature_22) ./ (feature_35 - feature_21)); ((newdata$feature_21 / newdata$feature_22) / (newdata$feature_35 - newdata$feature_21)); feature_21,feature_22,feature_35,feature_21
# Number of Samples Per Task
# Task, n_samples               
# all , 39                    
# Excluded Indexes: [ 5, 22, 29, 40, 43, 59, 89, 93, 94, 106, 113, 120, 139, 146, 151, 156, 157, 170, 174, 187, 192, 204, 236, 251, 266, 276, 279, 295, 300, 315, 327, 329, 338, 344, 346, 365, 366, 371, 385 ]

# Sample ID , Property Value        ,  Property Value (EST) ,  Feature 0 Value      ,  Feature 1 Value      ,  Feature 2 Value      ,  Feature 3 Value      
5           ,  9.466554641723630e-02, -1.237490452776145e-02,  1.227400623156293e+00,  6.650217737718128e+03,  0.000000000000000e+00, -2.512440944009393e+02
22          , -3.187671422958370e-02, -4.136137278893895e-03, -9.272832466742186e+00,  6.062027257941115e+02,  2.358499428053995e-03,  1.326401742191175e+01
29          , -1.075058937072754e-01,  1.720888421258047e-03,  3.166909394459694e+01,  2.955366371226817e+02,  9.312902170246802e-05,  1.051727184000851e+00
40          ,  8.543024063110349e-02, -1.105841659895744e-02,  3.791293450299923e+02,  1.349637371224708e+05,  2.036990823324127e-04, -1.353188089830065e+01
43          ,  3.863149225711820e-02, -1.182100878295581e-02,  3.538111208420813e+02,  1.545568643813805e+05,  8.715791815300722e-05, -3.712864714453157e+01
59          ,  3.299631357192980e-02, -1.017067004549777e-01, -1.987128895254238e+02,  5.328440945363705e+06,  1.341573678921137e-05, -5.238931553813319e+01
89          , -1.727662086486816e-01, -1.562840764658931e+00,  8.925010133853167e+00,  2.706186137769718e+02,  4.656115973481563e-01,  2.096760272715245e+01
93          , -8.234624862670700e-03,  1.241847096729436e-03,  1.547192226277095e-01,  6.260183243597630e+02,  5.759756256669337e-04,  6.910360027202898e+00
94          ,  3.739838123321520e-02, -7.450451582375638e-03,  1.571931902762449e+00,  3.055310680427682e+03,  3.545867033126846e-03,  3.011813700315033e+01
106         , -2.166919708251800e-03,  3.692087147806494e-03, -9.028257883852371e+00,  1.023121314008908e+02,  1.028739780934251e-03,  6.939745325422615e+01
113         ,  4.808726310729970e-02,  4.405601352227079e-03, -7.711743904325644e+01,  3.010521841010569e+03,  1.398781752013695e-04,  3.161009226268678e+00
120         ,  1.428375244140400e-03,  2.551048418558719e-03, -7.885609459015792e+00,  8.729144784301245e+02,  1.844877235834075e-04,  3.427536011300174e+00
139         , -3.616887092590330e-02,  4.508505659099675e-04,  8.490023486135417e+00,  1.845941315813140e+02,  7.919881640528209e-04,  9.243940908515077e+00
146         ,  1.264972686767590e-02,  3.152764215881782e-03, -6.132496346497685e+00,  1.496734485801564e+02,  1.556482703259819e-04,  1.238979794626133e+01
151         , -4.500385761260990e-02,  4.003460735757155e-03,  2.496823987286808e+00,  1.041195511715763e+03,  2.427760550778363e-04,  3.553593885792424e+01
156         ,  5.496220588684100e-03,  1.667514317950452e-03, -5.726853004614718e+00,  3.234721451489562e+03,  3.785588523385170e-04,  1.275850981727999e+00
157         , -7.247723817825311e-02,  1.276968965125240e-03, -1.639310718652967e+01,  5.911299395954910e+02,  8.379365392661613e-04,  1.519499749467101e+01
170         ,  5.851058483123770e-02,  2.951061041828529e-03, -1.427921678425056e+01,  4.458571697846151e+03,  4.591062968061031e-05,  7.887123244175265e-01
174         ,  5.844178199768000e-03, -3.709794907780522e-02, -1.485296822981801e+02,  2.119166914167925e+06,  1.284147450015067e-07, -2.271880102282117e+01
187         ,  2.110298633575430e-02,  2.919249211763213e-03, -1.370051808035211e+01,  9.557708700036475e+03,  5.872910349604037e-05,  2.931013589819728e+00
192         ,  2.425221443176270e-02, -6.023855721692266e-03, -8.558517702637776e+00,  4.746915949288050e+05,  2.295205532727654e-06,  7.690771801242554e+00
204         ,  1.126888275146480e-02,  2.292253169035089e-03,  2.492707774039160e+00,  1.698149561681318e+03,  3.206263814169269e-04,  1.147079528837664e+01
236         , -1.151049613952620e-02, -3.139279363250816e-02, -1.089678818494051e+01,  1.779089811957504e+06,  6.028805945940818e-06,  1.817292521376949e+01
251         , -2.776774406433110e-02,  7.927552396964449e-06, -9.679556111553561e+00,  2.349346022764147e+02,  1.096584623437077e-03,  1.125889580273221e+01
266         , -4.275083541869000e-04,  6.271948286933500e-03, -3.169107265975041e+01,  3.120198462089021e+04,  4.118525405366041e-05,  5.768197217552079e+01
276         ,  3.945925712585450e-02,  6.360014497906432e-04,  4.169688838081285e-01,  7.748127176068340e+04,  2.451896685809193e-04,  3.931326208117106e+00
279         ,  4.145424842834460e-02,  3.359448388372481e-03, -1.749641683275064e+01,  2.379166970706268e+03,  8.384538515447940e-05,  7.701763074616744e+00
295         , -2.707870721817010e-02, -1.260415742471173e-02,  2.065314334749326e+02,  7.031030287891748e+03,  2.945509205946612e-04, -1.507506728177557e+02
300         ,  2.831182956695550e-02, -2.052128116091743e-03,  3.343889747190488e+00,  2.459753776860905e+05,  6.058439452684020e-05,  6.095255592951237e+00
315         ,  4.429840087890620e-02, -1.177026612879323e-02,  3.248621522639548e+02,  4.775197696690114e+03,  1.911470469660317e-04, -9.287202282740424e+01
327         ,  1.536174297332770e-02,  6.733700207638687e-03, -1.613589329216846e+02,  1.219229024811836e+04,  3.543546020748794e-05,  3.407633273915394e+00
329         , -3.447393894195540e-02,  1.124517232538979e-03, -1.599139171550297e+01,  4.974019407335123e+02,  7.978148497790121e-04,  1.051869887603752e+01
338         , -1.185667872428895e-01, -2.121731672360773e-02,  5.582639040469565e+00,  1.210373661200192e+06,  1.029812997305437e-06,  5.174774522197035e+00
344         ,  3.363956689834590e-02, -2.010977976383277e-03, -2.031969722204565e+01,  1.067978390858601e+04,  1.630730482921337e-03,  6.498680574188772e+00
346         , -9.675530433654780e-02, -2.615925820320233e-01, -1.642855864486949e+02,  1.338380474610025e+07,  1.825541951606975e-07, -2.278668005711860e+01
365         ,  4.853692054748600e-03,  1.203248935694853e-03, -1.735228920165848e+01,  3.693115154357093e+02,  6.842083670007080e-04,  4.809540067062341e+00
366         ,  7.350245952606201e-02,  2.308230606430745e-03, -1.075324896124299e+01,  1.473869107906706e+03,  2.569141012358106e-04,  2.419632918949496e+00
371         ,  2.015067577362060e-02,  2.544422126412525e-03, -6.932855032839822e+00,  6.987201189210779e+03,  2.709930311767195e-04,  1.064527922297602e+01
385         ,  4.244400501251220e-02, -3.570899516669108e-03,  2.440507265791840e+02,  4.464806996446772e+03,  1.466332642711708e-04,  7.417556875663383e+00
