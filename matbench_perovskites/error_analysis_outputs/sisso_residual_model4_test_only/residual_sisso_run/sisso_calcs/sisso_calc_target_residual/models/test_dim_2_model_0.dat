# c0 + a0 * ((feature_7 * feature_6) / (|feature_48 - feature_19|)) + a1 * ((feature_21 / feature_22) / (feature_35 - feature_21))
# Property Label: $target$; Unit of the Property: Unitless
# RMSE: 0.226002910593903; Max AE: 1.37769863537096
# Coefficients
# Task   a0                      a1                      c0
# all , -3.332593411818851e+00,  5.921947657854406e-05, -1.239268933397635e-05, 
# Feature Rung, Units, and Expressions
# 0;  2; Unitless;                                         6|5|mult|47|18|abd|div; ((feature_7 * feature_6) / (|feature_48 - feature_19|)); $\left(\frac{ \left(feature_{7} feature_{6}\right) }{ \left(\left|feature_{48} - feature_{19}\right|\right) } \right)$; ((feature_7 .* feature_6) ./ abs(feature_48 - feature_19)); ((newdata$feature_7 * newdata$feature_6) / abs(newdata$feature_48 - newdata$feature_19)); feature_7,feature_6,feature_48,feature_19
# 1;  2; Unitless;                                         20|21|div|34|20|sub|div; ((feature_21 / feature_22) / (feature_35 - feature_21)); $\left(\frac{ \left(\frac{ feature_{21} }{ feature_{22} } \right) }{ \left(feature_{35} - feature_{21}\right) } \right)$; ((feature_21 ./ feature_22) ./ (feature_35 - feature_21)); ((newdata$feature_21 / newdata$feature_22) / (newdata$feature_35 - newdata$feature_21)); feature_21,feature_22,feature_35,feature_21
# Number of Samples Per Task
# Task, n_samples               
# all , 39                    
# Excluded Indexes: [ 5, 22, 29, 40, 43, 59, 89, 93, 94, 106, 113, 120, 139, 146, 151, 156, 157, 170, 174, 187, 192, 204, 236, 251, 266, 276, 279, 295, 300, 315, 327, 329, 338, 344, 346, 365, 366, 371, 385 ]

# Sample ID , Property Value        ,  Property Value (EST) ,  Feature 0 Value      ,  Feature 1 Value      
5           ,  9.466554641723630e-02, -1.489093645320792e-02,  0.000000000000000e+00, -2.512440944009393e+02
22          , -3.187671422958370e-02, -7.086824175990945e-03,  2.358499428053995e-03,  1.326401742191175e+01
29          , -1.075058937072754e-01, -2.604711201687996e-04,  9.312902170246802e-05,  1.051727184000851e+00
40          ,  8.543024063110349e-02, -1.492589813029084e-03,  2.036990823324127e-04, -1.353188089830065e+01
43          ,  3.863149225711820e-02, -2.501593643127149e-03,  8.715791815300722e-05, -3.712864714453157e+01
59          ,  3.299631357192980e-02, -3.159569729848831e-03,  1.341573678921137e-05, -5.238931553813319e+01
89          , -1.727662086486816e-01, -1.550464844019643e+00,  4.656115973481563e-01,  2.096760272715245e+01
93          , -8.234624862670700e-03, -1.522657361019591e-03,  5.759756256669337e-04,  6.910360027202898e+00
94          ,  3.739838123321520e-02, -1.004574549427072e-02,  3.545867033126846e-03,  3.011813700315033e+01
106         , -2.166919708251800e-03,  6.689167518478210e-04,  1.028739780934251e-03,  6.939745325422615e+01
113         ,  4.808726310729970e-02, -2.913564626277237e-04,  1.398781752013695e-04,  3.161009226268678e+00
120         ,  1.428375244140400e-03, -4.242383729661907e-04,  1.844877235834075e-04,  3.427536011300174e+00
139         , -3.616887092590330e-02, -2.104345884969661e-03,  7.919881640528209e-04,  9.243940908515077e+00
146         ,  1.264972686767590e-02,  2.026122597081731e-04,  1.556482703259819e-04,  1.238979794626133e+01
151         , -4.500385761260990e-02,  1.282953127859674e-03,  2.427760550778363e-04,  3.553593885792424e+01
156         ,  5.496220588684100e-03, -1.198420199292870e-03,  3.785588523385170e-04,  1.275850981727999e+00
157         , -7.247723817825311e-02, -1.905054681367968e-03,  8.379365392661613e-04,  1.519499749467101e+01
170         ,  5.851058483123770e-02, -1.186870203169805e-04,  4.591062968061031e-05,  7.887123244175265e-01
174         ,  5.844178199768000e-03, -1.358216148530710e-03,  1.284147450015067e-07, -2.271880102282117e+01
187         ,  2.110298633575430e-02, -3.453982209318421e-05,  5.872910349604037e-05,  2.931013589819728e+00
192         ,  2.425221443176270e-02,  4.354018043834958e-04,  2.295205532727654e-06,  7.690771801242554e+00
204         ,  1.126888275146480e-02, -4.016155627820512e-04,  3.206263814169269e-04,  1.147079528837664e+01
236         , -1.151049613952620e-02,  1.043706870749902e-03,  6.028805945940818e-06,  1.817292521376949e+01
251         , -2.776774406433110e-02, -3.000117464612066e-03,  1.096584623437077e-03,  1.125889580273221e+01
266         , -4.275083541869000e-04,  3.266249804595193e-03,  4.118525405366041e-05,  5.768197217552079e+01
276         ,  3.945925712585450e-02, -5.966990831885883e-04,  2.451896685809193e-04,  3.931326208117106e+00
279         ,  4.145424842834460e-02,  1.642791104995533e-04,  8.384538515447940e-05,  7.701763074616744e+00
295         , -2.707870721817010e-02, -9.921387084883762e-03,  2.945509205946612e-04, -1.507506728177557e+02
300         ,  2.831182956695550e-02,  1.466620024338574e-04,  6.058439452684020e-05,  6.095255592951237e+00
315         ,  4.429840087890620e-02, -6.149240659371076e-03,  1.911470469660317e-04, -9.287202282740424e+01
327         ,  1.536174297332770e-02,  7.131358828668037e-05,  3.543546020748794e-05,  3.407633273915394e+00
329         , -3.447393894195540e-02, -2.048273359832497e-03,  7.978148497790121e-04,  1.051869887603752e+01
338         , -1.185667872428895e-01,  2.906228012722920e-04,  1.029812997305437e-06,  5.174774522197035e+00
344         ,  3.363956689834590e-02, -5.062105891115188e-03,  1.630730482921337e-03,  6.498680574188772e+00
346         , -9.675530433654780e-02, -1.362416334187381e-03,  1.825541951606975e-07, -2.278668005711860e+01
365         ,  4.853692054748600e-03, -2.007762540156903e-03,  6.842083670007080e-04,  4.809540067062341e+00
366         ,  7.350245952606201e-02, -7.252935355433963e-04,  2.569141012358106e-04,  2.419632918949496e+00
371         ,  2.015067577362060e-02, -2.850944160652419e-04,  2.709930311767195e-04,  1.064527922297602e+01
385         ,  4.244400501251220e-02, -6.179790412920666e-05,  1.466332642711708e-04,  7.417556875663383e+00
